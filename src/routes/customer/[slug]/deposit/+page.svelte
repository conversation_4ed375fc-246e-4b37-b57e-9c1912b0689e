<script>
  import { onMount } from "svelte";
  import { goto } from "$app/navigation";
  import "../customer.css";
  import toastStore from '$lib/stores/toastStore';
  import QRCode from 'qrcode';

  /** @type {import('./$types').PageData} */
  export let data;
  const { deviceIp, wallets } = data;

  let bgLoaded = false;
  let selectedWallet = wallets && wallets.length > 0 ? wallets[0] : null;
  let qrCodeDataUrl = '';

  // Generate QR code for the selected wallet
  async function generateQRCode(address) {
    try {
      const dataUrl = await QRCode.toDataURL(address, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      qrCodeDataUrl = dataUrl;
    } catch (err) {
      console.error('Failed to generate QR code:', err);
      qrCodeDataUrl = '';
    }
  }

  // Copy wallet address to clipboard
  async function copyToClipboard(address, currency) {
    try {
      await navigator.clipboard.writeText(address);
      toastStore.add(`${currency} address copied to clipboard`, 'success');
    } catch (err) {
      console.error('Failed to copy address:', err);
      toastStore.add('Failed to copy address', 'error');
    }
  }

  // Handle wallet selection change
  function onWalletChange(event) {
    const selectedCurrency = event.target.value;
    selectedWallet = wallets.find(w => w.currency === selectedCurrency);
    if (selectedWallet) {
      generateQRCode(selectedWallet.address);
    }
  }

  onMount(() => {
    // Generate QR code for the initially selected wallet
    if (selectedWallet) {
      generateQRCode(selectedWallet.address);
    }

    /** @param {Event} e */
    const handleTouchMove = (e) => {
      // Cast target to HTMLElement to access tagName
      const target = /** @type {HTMLElement} */ (e.target);
      if (target.tagName !== "INPUT" && target.tagName !== "BUTTON") {
        e.preventDefault();
      }
    };

    // Add touchmove event listener with proper typing
    document.body.addEventListener("touchmove", handleTouchMove, false);

    return () => {
      document.body.removeEventListener("touchmove", handleTouchMove, false);
    };
  });
</script>

<svelte:head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="theme-color" content="#121212" />
  <title>Deposit - Phantom</title>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous" />
  <link rel="preload" as="image" href="/bg.webp" />
</svelte:head>

<div class="bg-image-container" class:loaded={bgLoaded}>
  <img src="/bg.webp" alt="Background" class="bg-image" loading="lazy" on:load={() => (bgLoaded = true)} crossorigin="anonymous" />
</div>

<div class="app-container">
  <div class="header-card">
    <div class="header">
      <div class="title-container">
        <img src="/favicon.webp" class="logo-image" alt="Phantom" width="36" height="36" loading="eager" />
        <h1>Add Funds</h1>
      </div>
    </div>
  </div>

  <div class="deposit-card">
    {#if wallets && wallets.length > 0}
      <div class="wallets-section">
        <h2>Deposit Funds</h2>
        <p class="deposit-instructions">Select a cryptocurrency and send funds to the address below.</p>

        <div class="currency-selector">
          <label for="currency-select">Select Currency:</label>
          <select
            id="currency-select"
            on:change={onWalletChange}
            value={selectedWallet?.currency || ''}
          >
            {#each wallets as wallet}
              <option value={wallet.currency}>{wallet.currency}</option>
            {/each}
          </select>
        </div>

        {#if selectedWallet}
          <div class="selected-wallet">
            <div class="qr-code-section">
              {#if qrCodeDataUrl}
                <img src={qrCodeDataUrl} alt="QR Code for {selectedWallet.currency} address" class="qr-code" />
              {:else}
                <div class="qr-placeholder">Generating QR code...</div>
              {/if}
            </div>

            <div class="wallet-address">
              <label for="wallet-address-display">Address:</label>
              <div class="address-container">
                <span id="wallet-address-display" class="address-text">{selectedWallet.address}</span>
                <button
                  class="copy-button"
                  on:click={() => copyToClipboard(selectedWallet.address, selectedWallet.currency)}
                  title="Copy address"
                >
                  📋
                </button>
              </div>
            </div>
          </div>
        {/if}

        <div class="deposit-notice">
          <p><strong>Important:</strong> Only send {selectedWallet?.currency || 'the selected cryptocurrency'} to this address. Sending the wrong currency may result in permanent loss of funds.</p>
        </div>
      </div>
    {:else}
      <div class="no-wallets">
        <h2>No Deposit Addresses Available</h2>
        <p>Wallet addresses are being generated for your team. Please try again in a few moments.</p>
      </div>
    {/if}
  </div>

  <button class="back-button" on:click|preventDefault={() => goto(`/customer/${deviceIp}`)}>
    ← Back
  </button>
</div>

<style>
  .wallets-section {
    text-align: center;
  }

  .wallets-section h2 {
    color: #ffffff;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
  }

  .deposit-instructions {
    color: #a0aec0;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .currency-selector {
    margin-bottom: 2rem;
  }

  .currency-selector label {
    display: block;
    color: #ffffff;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .currency-selector select {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
  }

  .currency-selector select:focus {
    outline: none;
    border-color: #ffd700;
    background: rgba(255, 255, 255, 0.15);
  }

  .currency-selector option {
    background: #1a1a1a;
    color: #ffffff;
  }

  .selected-wallet {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .qr-code-section {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .qr-code {
    display: block;
    max-width: 200px;
    height: auto;
  }

  .qr-placeholder {
    width: 200px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 0.9rem;
  }

  .wallet-address {
    width: 100%;
  }

  .wallet-address label {
    display: block;
    color: #ffffff;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .address-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 0.75rem;
  }

  .address-text {
    color: #ffffff;
    font-family: monospace;
    font-size: 0.85rem;
    word-break: break-all;
    flex: 1;
    text-align: left;
  }

  .copy-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 0.5rem;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .copy-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .copy-button:active {
    transform: scale(0.95);
  }

  .deposit-notice {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
  }

  .deposit-notice p {
    color: #ffc107;
    margin: 0;
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .no-wallets {
    text-align: center;
    padding: 2rem 1rem;
  }

  .no-wallets h2 {
    color: #ffffff;
    margin-bottom: 1rem;
  }

  .no-wallets p {
    color: #a0aec0;
    font-size: 0.9rem;
  }
</style>